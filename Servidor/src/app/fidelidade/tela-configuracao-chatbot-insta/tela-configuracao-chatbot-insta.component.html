<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <!-- Header -->
      <div class="card mb-3 header-card">
        <div class="card-header header-bg">
          <h4 class="card-title mb-0 text-dark">
            <i class="fas fa-robot mr-2"></i>
            Editor de Chatbot
          </h4>
          <small class="text-muted">Configure as mensagens e fluxo do seu assistente virtual</small>
        </div>
      </div>

      <!-- Mensagem de sucesso -->
      <div class="alert alert-success alert-dismissible" *ngIf="mensagemSucesso">
        <button type="button" class="btn-close" (click)="fecheMensagemSucesso()"></button>
        {{mensagemSucesso}}
      </div>

      <!-- Layout principal -->
      <div class="row">
        <!-- Sidebar esquerda - Lista de Respostas -->
        <div class="col-md-3">
          <div class="card sidebar-card">
            <div class="card-header sidebar-header">
              <h6 class="card-title mb-0 text-dark">Respostas</h6>
            </div>
            <div class="card-body p-0">
              <div class="list-group list-group-flush">
                <button
                  type="button"
                  class="list-group-item list-group-item-action d-flex justify-content-between align-items-start response-item"
                  [class.active]="respostaSelecionada === resposta.key"
                  *ngFor="let resposta of respostasDisponiveis"
                  (click)="selecionarResposta(resposta.key)">
                  <div class="ml-2 mr-auto">
                    <div class="font-weight-bold response-title">{{resposta.titulo}}</div>
                    <small class="response-connections">{{resposta.conexoes}} conexão(ões)</small>
                  </div>
                  <span class="response-badge">
                    <i class="{{resposta.icone}}"></i>
                  </span>
                </button>
              </div>
            </div>
          </div>

          <!-- Estatísticas -->
          <div class="card mt-3 sidebar-card">
            <div class="card-header sidebar-header">
              <h6 class="card-title mb-0 text-dark">Estatísticas:</h6>
            </div>
            <div class="card-body">
              <div class="d-flex align-items-center mb-2 stat-item">
                <i class="fas fa-reply stat-icon mr-2"></i>
                <span class="stat-text">{{estatisticas.respostas}} respostas</span>
              </div>
              <div class="d-flex align-items-center mb-2 stat-item">
                <i class="fas fa-circle stat-icon mr-2"></i>
                <span class="stat-text">{{estatisticas.botoes}} botões</span>
              </div>
              <div class="d-flex align-items-center mb-2 stat-item">
                <i class="fas fa-link stat-icon mr-2"></i>
                <span class="stat-text">{{estatisticas.conexoes}} conexões</span>
              </div>
              <div class="d-flex align-items-center stat-item">
                <i class="fas fa-external-link-alt stat-icon mr-2"></i>
                <span class="stat-text">{{estatisticas.links}} links</span>
              </div>
            </div>
          </div>

          <!-- Dicas -->
          <div class="card mt-3 sidebar-card">
            <div class="card-header sidebar-header">
              <h6 class="card-title mb-0 text-dark">Dicas:</h6>
            </div>
            <div class="card-body">
              <ul class="list-unstyled mb-0">
                <li class="mb-2">
                  <small class="tip-text">• Clique na mensagem para editar</small>
                </li>
                <li class="mb-2">
                  <small class="tip-text">• Clique no 🔧 dos botões para editar</small>
                </li>
                <li class="mb-2">
                  <small class="tip-text">• Clique nos botões azuis para navegar</small>
                </li>
                <li>
                  <small class="tip-text">• Use + para adicionar botões</small>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Área principal - Visualização da mensagem -->
        <div class="col-md-9">
          <div class="card main-card">
            <div class="card-header main-header d-flex justify-content-between align-items-center">
              <div class="d-flex align-items-center">
                <span class="main-badge mr-2">
                  <i class="fab fa-instagram"></i>
                </span>
                <span class="font-weight-bold main-title">
                  {{obterTituloResposta(respostaSelecionada)}}
                </span>
                <small class="main-subtitle ml-2">{{respostaSelecionada}}</small>
              </div>
              <button class="btn btn-edit btn-sm" (click)="iniciarEdicaoMensagem()">
                <i class="fas fa-edit mr-1"></i>
                Editar
              </button>
            </div>

            <div class="card-body">
              <!-- Simulação do chat do Instagram -->
              <div class="chat-container">
                <div class="chat-message bot-message">
                  <div class="message-content" (click)="iniciarEdicaoMensagem()" *ngIf="!editandoMensagem">
                    <div class="message-text">
                      {{obterMensagemResposta(respostaSelecionada)}}
                    </div>
                  </div>

                  <!-- Editor de mensagem -->
                  <div class="message-editor" *ngIf="editandoMensagem">
                    <textarea
                      class="form-control mb-2"
                      rows="4"
                      [(ngModel)]="mensagemEditavel"
                      placeholder="Digite a mensagem...">
                    </textarea>
                    <div class="d-flex">
                      <button class="btn btn-success btn-sm mr-2" (click)="salvarMensagem()">
                        <i class="fas fa-check mr-1"></i>
                        Salvar
                      </button>
                      <button class="btn btn-secondary btn-sm" (click)="cancelarEdicaoMensagem()">
                        <i class="fas fa-times mr-1"></i>
                        Cancelar
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Opções disponíveis -->
                <div class="options-section mt-3">
                  <div class="options-label">
                    <small class="text-muted">Opções disponíveis:</small>
                    <button class="btn btn-link btn-sm p-0 ml-2" (click)="iniciarCriacaoNovoBotao()" *ngIf="!editandoBotao">
                      <i class="fas fa-plus text-primary"></i>
                    </button>
                  </div>

                  <!-- Formulário de edição de botão -->
                  <div class="edit-button-form" *ngIf="editandoBotao">
                    <div class="form-container">
                      <h6 class="form-title">{{criandoNovoBotao ? 'Criando Novo Botão' : 'Editando Botão'}}</h6>

                      <div class="form-group">
                        <label class="form-label">Texto do Botão</label>
                        <input
                          type="text"
                          class="form-control"
                          [(ngModel)]="botaoEditor.texto"
                          placeholder="Digite o texto do botão">
                      </div>

                      <div class="form-group">
                        <label class="form-label">Tipo</label>
                        <select class="form-control" [(ngModel)]="botaoEditor.tipo">
                          <option *ngFor="let tipo of tiposBotao" [value]="tipo.valor">
                            {{tipo.texto}}
                          </option>
                        </select>
                      </div>

                      <div class="form-group" *ngIf="botaoEditor.tipo === 'resposta_rapida'">
                        <label class="form-label">Destino</label>
                        <select class="form-control" [(ngModel)]="botaoEditor.destino">
                          <option *ngFor="let destino of destinosDisponiveis" [value]="destino.valor">
                            {{destino.texto}}
                          </option>
                        </select>
                      </div>

                      <div class="form-group" *ngIf="botaoEditor.tipo === 'link_externo'">
                        <label class="form-label">URL</label>
                        <input
                          type="url"
                          class="form-control"
                          [(ngModel)]="botaoEditor.url"
                          placeholder="https://exemplo.com">
                      </div>

                      <div class="form-actions">
                        <button class="btn btn-success btn-sm mr-2" (click)="salvarBotao()">
                          <i class="fas fa-save mr-1"></i>
                          {{criandoNovoBotao ? 'Criar' : 'Salvar'}}
                        </button>
                        <button class="btn btn-danger btn-sm" (click)="removerBotao()" *ngIf="!criandoNovoBotao">
                          <i class="fas fa-times mr-1"></i>
                          Remover
                        </button>
                        <button class="btn btn-secondary btn-sm" (click)="cancelarEdicaoBotao()">
                          <i class="fas fa-times mr-1"></i>
                          Cancelar
                        </button>
                      </div>
                    </div>
                  </div>

                  <!-- Lista de opções -->
                  <div class="options-list mt-2" *ngIf="!editandoBotao">
                    <div class="option-item" *ngFor="let botao of obterBotoesResposta(respostaSelecionada); let i = index">
                      <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                          <span class="option-icon mr-2">
                            <i class="{{botao.icone}}"></i>
                          </span>
                          <span class="option-text">{{botao.texto}}</span>
                          <i class="fas fa-arrow-right text-muted ml-2"></i>
                          <span class="option-destination ml-1"
                                [class.text-primary]="botao.tipo === 'link_externo'"
                                [class.clickable-destination]="botao.tipo === 'resposta_rapida'"
                                (click)="botao.tipo === 'resposta_rapida' ? navegarParaResposta(botao.destino) : null">
                            {{botao.tipo === 'link_externo' ? botao.url : obterTextoDestino(botao.destino)}}
                          </span>
                        </div>
                        <button class="btn btn-link btn-sm p-0" (click)="iniciarEdicaoBotao(botao, i)">
                          <i class="fas fa-wrench text-muted"></i>
                        </button>
                      </div>
                    </div>

                    <!-- Mensagem quando não há opções -->
                    <div class="no-options-message" *ngIf="obterBotoesResposta(respostaSelecionada).length === 0">
                      <p class="text-muted text-center mb-0">
                        <i class="fas fa-info-circle mr-2"></i>
                        Nenhuma opção configurada. Clique no + para adicionar uma nova opção.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
