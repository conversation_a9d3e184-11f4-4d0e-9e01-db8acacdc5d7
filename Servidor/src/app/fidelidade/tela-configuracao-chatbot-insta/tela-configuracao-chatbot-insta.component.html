<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h4 class="card-title">
            <i class="fab fa-instagram"></i>
            Configuração do Chatbot Instagram
          </h4>
        </div>
        <div class="card-body">
          <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            Componente em desenvolvimento. Configure aqui as opções do chatbot para Instagram.
          </div>
          
          <!-- Mensagem de sucesso -->
          <div class="alert alert-success" *ngIf="mensagemSucesso">
            <button type="button" class="close" (click)="fecheMensagemSucesso()">
              <span>&times;</span>
            </button>
            {{mensagemSucesso}}
          </div>
          
          <!-- Conteúdo do componente será implementado aqui -->
          <div class="text-center mt-4">
            <p class="text-muted">Funcionalidades do chatbot Instagram serão implementadas em breve.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
