import {Component, OnInit} from '@angular/core';
import {ConstantsService} from "../ConstantsService";
import {AutorizacaoService} from "../../services/autorizacao.service";

@Component({
  selector: 'app-tela-configuracao-chatbot-insta',
  templateUrl: './tela-configuracao-chatbot-insta.component.html',
  styleUrls: ['./tela-configuracao-chatbot-insta.component.scss']
})
export class TelaConfiguracaoChatbotInstaComponent implements OnInit {
  empresa: any;
  usuario: any;
  salvando: boolean = false;
  mensagemSucesso: any;
  respostaSelecionada: string = 'get_started';
  editandoMensagem: boolean = false;
  mensagemEditavel: string = '';
  editandoBotao: boolean = false;
  botaoSendoEditado: any = null;
  indiceBotaoEditado: number = -1;
  criandoNovoBotao: boolean = false;

  // Dad<PERSON> das respostas baseadas no arquivo de locales
  respostasDisponiveis = [
    {
      key: 'get_started',
      titulo: 'Boas Vindas',
      conexoes: 3,
      icone: 'fas fa-handshake'
    },
    {
      key: 'fazer_pedido',
      titulo: 'Fazer Pedido',
      conexoes: 1,
      icone: 'fas fa-shopping-cart'
    },
    {
      key: 'cardapio',
      titulo: 'Cardápio',
      conexoes: 3,
      icone: 'fas fa-utensils'
    },
    {
      key: 'atendente',
      titulo: 'Atendente',
      conexoes: 2,
      icone: 'fas fa-headset'
    },
    {
      key: 'horario_atendimento',
      titulo: 'Horário de Atendimento',
      conexoes: 2,
      icone: 'fas fa-clock'
    },
    {
      key: 'agradecimento',
      titulo: 'Agradecimento',
      conexoes: 2,
      icone: 'fas fa-heart'
    },
    {
      key: 'fallback',
      titulo: 'Fallback',
      conexoes: 3,
      icone: 'fas fa-question-circle'
    }
  ];

  // Mensagens baseadas no arquivo de locales
  mensagensResposta = {
    get_started: {
      mensagem: 'Olá! Bem-vindo a {{nomeEmpresa}} 😊\n\nA qualquer momento, você pode usar o menu abaixo para que eu possa te ajudar.\n\nO que você deseja?'
    },
    fazer_pedido: {
      mensagem: 'Clique no botão abaixo para fazer o seu pedido.'
    },
    cardapio: {
      mensagem: 'Segue nosso cardápio 🍽'
    },
    atendente: {
      mensagem: 'Olá, em breve um de nossos atendentes irá te responder.\n\nVocê gostaria de falar sobre o quê?'
    },
    horario_atendimento: {
      mensagem: 'Segunda a Sexta das 18:00 às 22:00.'
    },
    agradecimento: {
      mensagem: 'Por nada. Estou sempre por aqui. Só mandar um oi. :-)'
    },
    fallback: {
      mensagem: 'Desculpe-me, mas eu não entendo "{{message}}".'
    }
  };

  // Opções para os formulários
  tiposBotao = [
    { valor: 'resposta_rapida', texto: 'Resposta Rápida' },
    { valor: 'link_externo', texto: 'Link externo' }
  ];

  destinosDisponiveis = [
    { valor: 'get_started', texto: 'Boas Vindas' },
    { valor: 'fazer_pedido', texto: 'Fazer Pedido' },
    { valor: 'cardapio', texto: 'Cardápio' },
    { valor: 'atendente', texto: 'Atendente' },
    { valor: 'horario_atendimento', texto: 'Horário de Atendimento' },
    { valor: 'agradecimento', texto: 'Agradecimento' }
  ];

  // Editor de botão
  botaoEditor = {
    texto: '',
    tipo: 'resposta_rapida',
    destino: '',
    url: ''
  };

  // Estatísticas
  estatisticas = {
    respostas: 7,
    botoes: 17,
    conexoes: 16,
    links: 1
  };

  // Botões das respostas
  botoesRespostas = {
    get_started: [
      { texto: 'Ver Cardápio', tipo: 'resposta_rapida', destino: 'cardapio', icone: 'fas fa-eye' },
      { texto: 'Fazer Pedido', tipo: 'resposta_rapida', destino: 'fazer_pedido', icone: 'fas fa-shopping-cart' },
      { texto: 'Horários de Funcionamento', tipo: 'resposta_rapida', destino: 'horario_atendimento', icone: 'fas fa-clock' }
    ],
    fazer_pedido: [
      { texto: 'Acessar Cardápio', tipo: 'link_externo', url: 'https://exemplo.com/cardapio', icone: 'fas fa-external-link-alt' }
    ],
    cardapio: [
      { texto: 'Fazer Pedido', tipo: 'resposta_rapida', destino: 'fazer_pedido', icone: 'fas fa-shopping-cart' },
      { texto: 'Falar com Atendente', tipo: 'resposta_rapida', destino: 'atendente', icone: 'fas fa-headset' }
    ],
    atendente: [
      { texto: 'Voltar ao Menu', tipo: 'resposta_rapida', destino: 'get_started', icone: 'fas fa-home' }
    ],
    horario_atendimento: [
      { texto: 'Fazer Pedido', tipo: 'resposta_rapida', destino: 'fazer_pedido', icone: 'fas fa-shopping-cart' },
      { texto: 'Ver Cardápio', tipo: 'resposta_rapida', destino: 'cardapio', icone: 'fas fa-eye' }
    ],
    agradecimento: [
      { texto: 'Voltar ao Menu', tipo: 'resposta_rapida', destino: 'get_started', icone: 'fas fa-home' }
    ],
    fallback: [
      { texto: 'Voltar ao Menu', tipo: 'resposta_rapida', destino: 'get_started', icone: 'fas fa-home' },
      { texto: 'Falar com Atendente', tipo: 'resposta_rapida', destino: 'atendente', icone: 'fas fa-headset' }
    ]
  };

  constructor(private constantsService: ConstantsService,
              private autorizacaoService: AutorizacaoService) {
  }

  ngOnInit() {
    this.usuario = this.autorizacaoService.getUser();

    this.constantsService.empresa$.subscribe( empresa => {
      if(empresa){
        this.empresa = empresa;
      }
    });
  }

  selecionarResposta(key: string) {
    this.respostaSelecionada = key;
    this.editandoMensagem = false;
    this.cancelarEdicaoBotao(); // Cancela edição de botão ao trocar de resposta
  }

  obterMensagemResposta(key: string): string {
    if (!this.mensagensResposta[key]) {
      return '';
    }

    let mensagem = this.mensagensResposta[key].mensagem;

    // Substitui placeholder da empresa
    if (this.empresa && mensagem.includes('{{nomeEmpresa}}')) {
      mensagem = mensagem.replace('{{nomeEmpresa}}', this.empresa.nome || 'Sua Empresa');
    }

    return mensagem;
  }

  iniciarEdicaoMensagem() {
    this.editandoMensagem = true;
    this.mensagemEditavel = this.obterMensagemResposta(this.respostaSelecionada);
  }

  salvarMensagem() {
    if (this.respostaSelecionada && this.mensagensResposta[this.respostaSelecionada]) {
      this.mensagensResposta[this.respostaSelecionada].mensagem = this.mensagemEditavel;
    }
    this.editandoMensagem = false;
  }

  cancelarEdicaoMensagem() {
    this.editandoMensagem = false;
    this.mensagemEditavel = '';
  }

  obterBotoesResposta(key: string) {
    return this.botoesRespostas[key] || [];
  }

  obterTituloResposta(key: string): string {
    const resposta = this.respostasDisponiveis.find(r => r.key === key);
    return resposta ? resposta.titulo : 'Resposta';
  }

  iniciarEdicaoBotao(botao: any, indice: number) {
    this.editandoBotao = true;
    this.criandoNovoBotao = false;
    this.botaoSendoEditado = botao;
    this.indiceBotaoEditado = indice;
    this.botaoEditor = {
      texto: botao.texto || '',
      tipo: botao.tipo || 'resposta_rapida',
      destino: botao.destino || '',
      url: botao.url || ''
    };
  }

  iniciarCriacaoNovoBotao() {
    this.editandoBotao = true;
    this.criandoNovoBotao = true;
    this.botaoSendoEditado = null;
    this.indiceBotaoEditado = -1;
    this.botaoEditor = {
      texto: '',
      tipo: 'resposta_rapida',
      destino: this.destinosDisponiveis[0]?.valor || '',
      url: ''
    };
  }

  salvarBotao() {
    if (this.criandoNovoBotao) {
      // Criando novo botão
      if (!this.botoesRespostas[this.respostaSelecionada]) {
        this.botoesRespostas[this.respostaSelecionada] = [];
      }

      const novoBotao = {
        texto: this.botaoEditor.texto,
        tipo: this.botaoEditor.tipo,
        destino: this.botaoEditor.destino,
        url: this.botaoEditor.url,
        icone: this.obterIconePadrao(this.botaoEditor.tipo)
      };

      this.botoesRespostas[this.respostaSelecionada].push(novoBotao);
    } else if (this.botaoSendoEditado && this.indiceBotaoEditado >= 0) {
      // Editando botão existente
      const botoes = this.botoesRespostas[this.respostaSelecionada];
      if (botoes && botoes[this.indiceBotaoEditado]) {
        botoes[this.indiceBotaoEditado] = {
          ...botoes[this.indiceBotaoEditado],
          texto: this.botaoEditor.texto,
          tipo: this.botaoEditor.tipo,
          destino: this.botaoEditor.destino,
          url: this.botaoEditor.url
        };
      }
    }
    this.cancelarEdicaoBotao();
  }

  removerBotao() {
    if (this.botaoSendoEditado && this.indiceBotaoEditado >= 0) {
      const botoes = this.botoesRespostas[this.respostaSelecionada];
      if (botoes) {
        botoes.splice(this.indiceBotaoEditado, 1);
      }
    }
    this.cancelarEdicaoBotao();
  }

  cancelarEdicaoBotao() {
    this.editandoBotao = false;
    this.criandoNovoBotao = false;
    this.botaoSendoEditado = null;
    this.indiceBotaoEditado = -1;
    this.botaoEditor = {
      texto: '',
      tipo: 'resposta_rapida',
      destino: '',
      url: ''
    };
  }

  obterIconePadrao(tipo: string): string {
    switch (tipo) {
      case 'link_externo':
        return 'fas fa-external-link-alt';
      case 'resposta_rapida':
      default:
        return 'fas fa-reply';
    }
  }

  obterTextoDestino(destino: string): string {
    const destinoObj = this.destinosDisponiveis.find(d => d.valor === destino);
    return destinoObj ? destinoObj.texto : destino;
  }

  navegarParaResposta(destino: string) {
    // Verifica se o destino é uma resposta válida
    const respostaValida = this.respostasDisponiveis.find(r => r.key === destino);
    if (respostaValida) {
      this.selecionarResposta(destino);
    }
  }

  fecheMensagemSucesso() {
    delete this.mensagemSucesso;
  }
}
