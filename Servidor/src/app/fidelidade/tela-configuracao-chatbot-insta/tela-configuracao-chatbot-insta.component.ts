import {Component, OnInit} from '@angular/core';
import {ConstantsService} from "../ConstantsService";
import {AutorizacaoService} from "../../services/autorizacao.service";

@Component({
  selector: 'app-tela-configuracao-chatbot-insta',
  templateUrl: './tela-configuracao-chatbot-insta.component.html',
  styleUrls: ['./tela-configuracao-chatbot-insta.component.scss']
})
export class TelaConfiguracaoChatbotInstaComponent implements OnInit {
  empresa: any;
  usuario: any;
  salvando: boolean = false;
  mensagemSucesso: any;
  respostaSelecionada: string = 'get_started';
  editandoMensagem: boolean = false;
  mensagemEditavel: string = '';

  // Dados das respostas baseadas no arquivo de locales
  respostasDisponiveis = [
    {
      key: 'get_started',
      titulo: 'Boas Vindas',
      conexoes: 3,
      icone: 'fas fa-hand-wave',
      cor: 'primary'
    },
    {
      key: 'fazer_pedido',
      titulo: 'Fazer Pedido',
      conexoes: 1,
      icone: 'fas fa-shopping-cart',
      cor: 'success'
    },
    {
      key: 'cardapio',
      titulo: 'Cardápio',
      conexoes: 3,
      icone: 'fas fa-utensils',
      cor: 'info'
    },
    {
      key: 'atendente',
      titulo: 'Atendente',
      conexoes: 2,
      icone: 'fas fa-user-headset',
      cor: 'warning'
    },
    {
      key: 'horario_atendimento',
      titulo: 'Horário de Atendimento',
      conexoes: 2,
      icone: 'fas fa-clock',
      cor: 'secondary'
    },
    {
      key: 'agradecimento',
      titulo: 'Agradecimento',
      conexoes: 2,
      icone: 'fas fa-heart',
      cor: 'danger'
    },
    {
      key: 'fallback',
      titulo: 'Fallback',
      conexoes: 3,
      icone: 'fas fa-question-circle',
      cor: 'dark'
    }
  ];

  // Mensagens baseadas no arquivo de locales
  mensagensResposta = {
    get_started: {
      mensagem: 'Olá! Bem-vindo a {{nomeEmpresa}} 😊\n\nA qualquer momento, você pode usar o menu abaixo para que eu possa te ajudar.\n\nO que você deseja?'
    },
    fazer_pedido: {
      mensagem: 'Clique no botão abaixo para fazer o seu pedido.'
    },
    cardapio: {
      mensagem: 'Segue nosso cardápio 🍽'
    },
    atendente: {
      mensagem: 'Olá, em breve um de nossos atendentes irá te responder.\n\nVocê gostaria de falar sobre o quê?'
    },
    horario_atendimento: {
      mensagem: 'Segunda a Sexta das 18:00 às 22:00.'
    },
    agradecimento: {
      mensagem: 'Por nada. Estou sempre por aqui. Só mandar um oi. :-)'
    },
    fallback: {
      mensagem: 'Desculpe-me, mas eu não entendo "{{message}}".'
    }
  };

  // Estatísticas
  estatisticas = {
    respostas: 7,
    botoes: 17,
    conexoes: 16,
    links: 1
  };

  constructor(private constantsService: ConstantsService,
              private autorizacaoService: AutorizacaoService) {
  }

  ngOnInit() {
    this.usuario = this.autorizacaoService.getUser();

    this.constantsService.empresa$.subscribe( empresa => {
      if(empresa){
        this.empresa = empresa;
      }
    });
  }

  selecionarResposta(key: string) {
    this.respostaSelecionada = key;
    this.editandoMensagem = false;
  }

  obterMensagemResposta(key: string): string {
    if (!this.mensagensResposta[key]) {
      return '';
    }

    let mensagem = this.mensagensResposta[key].mensagem;

    // Substitui placeholder da empresa
    if (this.empresa && mensagem.includes('{{nomeEmpresa}}')) {
      mensagem = mensagem.replace('{{nomeEmpresa}}', this.empresa.nome || 'Sua Empresa');
    }

    return mensagem;
  }

  iniciarEdicaoMensagem() {
    this.editandoMensagem = true;
    this.mensagemEditavel = this.obterMensagemResposta(this.respostaSelecionada);
  }

  salvarMensagem() {
    if (this.respostaSelecionada && this.mensagensResposta[this.respostaSelecionada]) {
      this.mensagensResposta[this.respostaSelecionada].mensagem = this.mensagemEditavel;
    }
    this.editandoMensagem = false;
  }

  cancelarEdicaoMensagem() {
    this.editandoMensagem = false;
    this.mensagemEditavel = '';
  }

  obterBotoesResposta(key: string) {
    // Simula botões para a resposta "get_started" (Boas Vindas)
    if (key === 'get_started') {
      return [
        { texto: 'Ver Cardápio', destino: 'cardapio', icone: 'fas fa-eye' },
        { texto: 'Fazer Pedido', destino: 'fazer_pedido', icone: 'fas fa-shopping-cart' },
        { texto: 'Horários de Funcionamento', destino: 'horario_atendimento', icone: 'fas fa-clock' }
      ];
    }
    return [];
  }

  obterTituloResposta(key: string): string {
    const resposta = this.respostasDisponiveis.find(r => r.key === key);
    return resposta ? resposta.titulo : 'Resposta';
  }

  fecheMensagemSucesso() {
    delete this.mensagemSucesso;
  }
}
