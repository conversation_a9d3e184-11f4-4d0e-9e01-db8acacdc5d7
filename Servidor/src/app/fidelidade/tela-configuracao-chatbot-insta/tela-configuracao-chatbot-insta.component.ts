import {Component, OnInit} from '@angular/core';
import {ConstantsService} from "../ConstantsService";
import {AutorizacaoService} from "../../services/autorizacao.service";

@Component({
  selector: 'app-tela-configuracao-chatbot-insta',
  templateUrl: './tela-configuracao-chatbot-insta.component.html',
  styleUrls: ['./tela-configuracao-chatbot-insta.component.scss']
})
export class TelaConfiguracaoChatbotInstaComponent implements OnInit {
  empresa: any;
  usuario: any;
  salvando: boolean = false;
  mensagemSucesso: any;

  constructor(private constantsService: ConstantsService, 
              private autorizacaoService: AutorizacaoService) {
  }

  ngOnInit() {
    this.usuario = this.autorizacaoService.getUser();

    this.constantsService.empresa$.subscribe( empresa => {
      if(empresa){
        this.empresa = empresa;
      }
    });
  }

  fecheMensagemSucesso() {
    delete this.mensagemSucesso;
  }
}
