// Estilos específicos para o componente de configuração do chatbot Instagram

// Chat container
.chat-container {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  min-height: 400px;
}

// Mensagem do bot
.bot-message {
  max-width: 80%;
  margin-bottom: 15px;
}

.message-content {
  background: #007bff;
  color: white;
  padding: 12px 16px;
  border-radius: 18px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #0056b3;
  }
}

.message-text {
  white-space: pre-wrap;
  line-height: 1.4;
}

// Editor de mensagem
.message-editor {
  background: white;
  border: 2px solid #007bff;
  border-radius: 8px;
  padding: 15px;
}

// Seção de opções
.options-section {
  border-top: 1px solid #dee2e6;
  padding-top: 15px;
}

.options-label {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.options-list {
  .option-item {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 8px;
    transition: all 0.2s ease;

    &:hover {
      border-color: #007bff;
      box-shadow: 0 2px 4px rgba(0,123,255,0.1);
    }
  }

  .option-icon {
    color: #6c757d;
    width: 20px;
    text-align: center;
  }

  .option-text {
    font-weight: 500;
  }

  .option-destination {
    font-size: 0.875rem;
    font-weight: 500;
  }
}

// Lista de respostas
.list-group-item {
  border-left: 3px solid transparent;
  transition: all 0.2s ease;

  &.active {
    border-left-color: #007bff;
    background-color: rgba(0,123,255,0.1);
  }

  &:hover:not(.active) {
    border-left-color: #6c757d;
    background-color: rgba(0,0,0,0.05);
  }
}

// Cards
.card {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border: none;

  .card-header {
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
  }
}

// Badges personalizados para Bootstrap 4
.badge {
  font-size: 0.75rem;

  &.badge-primary { background-color: #007bff !important; }
  &.badge-success { background-color: #28a745 !important; }
  &.badge-info { background-color: #17a2b8 !important; }
  &.badge-warning { background-color: #ffc107 !important; }
  &.badge-danger { background-color: #dc3545 !important; }
  &.badge-secondary { background-color: #6c757d !important; }
  &.badge-dark { background-color: #343a40 !important; }
}

// Botões
.btn-link {
  text-decoration: none;

  &:hover {
    text-decoration: none;
  }
}

// Responsividade
@media (max-width: 768px) {
  .col-md-3 {
    margin-bottom: 20px;
  }

  .chat-container {
    padding: 15px;
  }

  .bot-message {
    max-width: 95%;
  }
}
