// Estilos específicos para o componente de configuração do chatbot Instagram
// Baseado na imagem fornecida com cores mais fiéis

// Variáveis de cores baseadas na imagem
$primary-blue: #4285f4;
$light-blue: #e8f0fe;
$sidebar-bg: #f8f9fa;
$border-color: #e0e0e0;
$text-dark: #333333;
$text-muted: #666666;
$white: #ffffff;
$selected-bg: #e3f2fd;

// Header principal
.header-card {
  border: none;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.header-bg {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: none;
  padding: 1rem 1.5rem;
}

// Sidebar
.sidebar-card {
  border: 1px solid $border-color;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  background: $white;
}

.sidebar-header {
  background: $sidebar-bg;
  border-bottom: 1px solid $border-color;
  padding: 0.75rem 1rem;
}

// Lista de respostas
.response-item {
  border: none;
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 16px;
  transition: all 0.2s ease;
  background: $white;

  &:hover:not(.active) {
    background: #f8f9fa;
    border-left: 3px solid #dee2e6;
  }

  &.active {
    background: $selected-bg;
    border-left: 3px solid $primary-blue;

    .response-title {
      color: $primary-blue;
    }
  }
}

.response-title {
  color: $text-dark;
  font-size: 0.9rem;
}

.response-connections {
  color: $text-muted;
  font-size: 0.75rem;
}

.response-badge {
  background: $light-blue;
  color: $primary-blue;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
}

// Estatísticas
.stat-item {
  padding: 4px 0;
}

.stat-icon {
  color: $primary-blue;
  width: 16px;
  font-size: 0.85rem;
}

.stat-text {
  color: $text-dark;
  font-size: 0.85rem;
}

// Dicas
.tip-text {
  color: $text-muted;
  font-size: 0.8rem;
}

// Área principal
.main-card {
  border: 1px solid $border-color;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.main-header {
  background: $white;
  border-bottom: 1px solid $border-color;
  padding: 1rem 1.5rem;
}

.main-badge {
  background: $primary-blue;
  color: $white;
  padding: 6px 10px;
  border-radius: 16px;
  font-size: 0.9rem;
}

.main-title {
  color: $text-dark;
  font-size: 1.1rem;
}

.main-subtitle {
  color: $text-muted;
  font-size: 0.85rem;
  background: #f0f0f0;
  padding: 2px 8px;
  border-radius: 8px;
}

.btn-edit {
  background: $white;
  border: 1px solid $primary-blue;
  color: $primary-blue;

  &:hover {
    background: $primary-blue;
    color: $white;
  }
}

// Chat container
.chat-container {
  background: #fafbfc;
  border-radius: 8px;
  padding: 20px;
  min-height: 400px;
  border: 1px solid #f0f0f0;
}

// Mensagem do bot
.bot-message {
  max-width: 80%;
  margin-bottom: 15px;
}

.message-content {
  background: $primary-blue;
  color: $white;
  padding: 12px 16px;
  border-radius: 18px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);

  &:hover {
    background: darken($primary-blue, 10%);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
  }
}

.message-text {
  white-space: pre-wrap;
  line-height: 1.4;
  font-size: 0.9rem;
}

// Editor de mensagem
.message-editor {
  background: $white;
  border: 2px solid $primary-blue;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

// Seção de opções
.options-section {
  border-top: 1px solid #e8e8e8;
  padding-top: 15px;
  margin-top: 15px;
}

.options-label {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  small {
    color: $text-muted;
    font-weight: 500;
  }
}

.options-list {
  .option-item {
    background: $white;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 8px;
    transition: all 0.2s ease;

    &:hover {
      border-color: $primary-blue;
      box-shadow: 0 2px 4px rgba(66, 133, 244, 0.1);
      transform: translateY(-1px);
    }
  }

  .option-icon {
    color: $text-muted;
    width: 20px;
    text-align: center;
  }

  .option-text {
    font-weight: 500;
    color: $text-dark;
  }

  .option-destination {
    font-size: 0.875rem;
    font-weight: 500;
    color: $primary-blue;

    &.clickable-destination {
      cursor: pointer;
      padding: 2px 6px;
      border-radius: 4px;
      background: rgba(66, 133, 244, 0.1);
      transition: all 0.2s ease;

      &:hover {
        background: rgba(66, 133, 244, 0.2);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(66, 133, 244, 0.2);
      }
    }
  }
}

// Formulário de edição de botão
.edit-button-form {
  margin-top: 15px;
}

.form-container {
  background: $light-blue;
  border: 2px solid $primary-blue;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
}

.form-title {
  color: $primary-blue;
  font-weight: 600;
  margin-bottom: 15px;
  font-size: 1rem;
}

.form-group {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  color: $text-dark;
  font-weight: 500;
  margin-bottom: 5px;
  font-size: 0.9rem;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  font-size: 0.9rem;
  background: $white;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: $primary-blue;
    box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.1);
  }

  &::placeholder {
    color: #999;
  }
}

select.form-control {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 32px;
  appearance: none;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  justify-content: flex-end;
}

.btn-success {
  background: #28a745;
  border-color: #28a745;
  color: $white;

  &:hover {
    background: #218838;
    border-color: #1e7e34;
  }
}

.btn-danger {
  background: #dc3545;
  border-color: #dc3545;
  color: $white;

  &:hover {
    background: #c82333;
    border-color: #bd2130;
  }
}

.btn-secondary {
  background: #6c757d;
  border-color: #6c757d;
  color: $white;

  &:hover {
    background: #5a6268;
    border-color: #545b62;
  }
}

// Mensagem quando não há opções
.no-options-message {
  padding: 20px;
  background: #f8f9fa;
  border: 1px dashed #dee2e6;
  border-radius: 8px;
  margin-top: 10px;

  p {
    font-size: 0.9rem;
  }
}

// Responsividade
@media (max-width: 768px) {
  .col-md-3 {
    margin-bottom: 20px;
  }

  .chat-container {
    padding: 15px;
  }

  .bot-message {
    max-width: 95%;
  }

  .main-header {
    padding: 0.75rem 1rem;
  }

  .header-bg {
    padding: 0.75rem 1rem;
  }
}
